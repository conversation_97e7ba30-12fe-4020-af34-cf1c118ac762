import { db } from '@/lib/supabase'

// Default exchange rates (fallback values)
const DEFAULT_RATES = {
  'USD-KWD': 0.31,
  'GBP-KWD': 0.38,
  'KWD-USD': 3.25,
  'KWD-GBP': 2.63,
}

export interface ExchangeRate {
  from_currency: string
  to_currency: string
  rate: number
  date: string
  source?: string
}

export class CurrencyConverter {
  private static rateCache: Map<string, { rate: number; timestamp: number }> = new Map()
  private static readonly CACHE_DURATION = 1000 * 60 * 60 // 1 hour

  /**
   * Get exchange rate from cache or database
   */
  static async getExchangeRate(fromCurrency: string, toCurrency: string): Promise<number> {
    if (fromCurrency === toCurrency) return 1

    const cacheKey = `${fromCurrency}-${toCurrency}`
    const cached = this.rateCache.get(cacheKey)
    
    // Check if we have a valid cached rate
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.rate
    }

    try {
      // Try to get the latest rate from database
      const { data, error } = await db.exchangeRates.getLatest(fromCurrency, toCurrency)
      
      if (!error && data) {
        const rate = data.rate
        this.rateCache.set(cacheKey, { rate, timestamp: Date.now() })
        return rate
      }
    } catch (error) {
      console.warn('Failed to fetch exchange rate from database:', error)
    }

    // Fall back to default rates
    const defaultRate = DEFAULT_RATES[cacheKey as keyof typeof DEFAULT_RATES]
    if (defaultRate) {
      this.rateCache.set(cacheKey, { rate: defaultRate, timestamp: Date.now() })
      return defaultRate
    }

    // If no default rate available, try inverse rate
    const inverseKey = `${toCurrency}-${fromCurrency}`
    const inverseRate = DEFAULT_RATES[inverseKey as keyof typeof DEFAULT_RATES]
    if (inverseRate) {
      const rate = 1 / inverseRate
      this.rateCache.set(cacheKey, { rate, timestamp: Date.now() })
      return rate
    }

    throw new Error(`No exchange rate available for ${fromCurrency} to ${toCurrency}`)
  }

  /**
   * Convert amount from one currency to another
   */
  static async convert(
    amount: number,
    fromCurrency: string,
    toCurrency: string
  ): Promise<{ convertedAmount: number; rate: number }> {
    const rate = await this.getExchangeRate(fromCurrency, toCurrency)
    const convertedAmount = amount * rate
    
    return {
      convertedAmount: Math.round(convertedAmount * 100) / 100, // Round to 2 decimal places
      rate,
    }
  }

  /**
   * Convert amount to KWD (the base currency for the app)
   */
  static async convertToKWD(amount: number, fromCurrency: string): Promise<{ kwdAmount: number; rate: number }> {
    if (fromCurrency === 'KWD') {
      return { kwdAmount: amount, rate: 1 }
    }

    const { convertedAmount, rate } = await this.convert(amount, fromCurrency, 'KWD')
    return { kwdAmount: convertedAmount, rate }
  }

  /**
   * Convert amount from KWD to another currency
   */
  static async convertFromKWD(kwdAmount: number, toCurrency: string): Promise<{ convertedAmount: number; rate: number }> {
    if (toCurrency === 'KWD') {
      return { convertedAmount: kwdAmount, rate: 1 }
    }

    const { convertedAmount, rate } = await this.convert(kwdAmount, 'KWD', toCurrency)
    return { convertedAmount, rate }
  }

  /**
   * Format currency amount with proper symbol and formatting
   */
  static formatCurrency(amount: number, currency: string): string {
    const symbols = {
      KWD: 'KWD',
      USD: '$',
      GBP: '£',
    }

    const symbol = symbols[currency as keyof typeof symbols] || currency
    const formattedAmount = new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(Math.abs(amount))

    return `${symbol} ${formattedAmount}`
  }

  /**
   * Update exchange rate in database
   */
  static async updateExchangeRate(
    fromCurrency: string,
    toCurrency: string,
    rate: number,
    source?: string
  ): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0]
      
      await db.exchangeRates.create({
        from_currency: fromCurrency,
        to_currency: toCurrency,
        rate,
        date: today,
        source,
      })

      // Update cache
      const cacheKey = `${fromCurrency}-${toCurrency}`
      this.rateCache.set(cacheKey, { rate, timestamp: Date.now() })
    } catch (error) {
      console.error('Failed to update exchange rate:', error)
      throw error
    }
  }

  /**
   * Fetch latest rates from external API (placeholder for future implementation)
   */
  static async fetchLatestRates(): Promise<void> {
    // This would integrate with a real exchange rate API like:
    // - exchangerate-api.com
    // - fixer.io
    // - currencylayer.com
    
    console.log('Fetching latest exchange rates... (not implemented)')
    
    // For now, we'll use the default rates
    const today = new Date().toISOString().split('T')[0]
    
    try {
      // Update default rates in database
      for (const [pair, rate] of Object.entries(DEFAULT_RATES)) {
        const [from, to] = pair.split('-')
        await this.updateExchangeRate(from, to, rate, 'default')
      }
    } catch (error) {
      console.error('Failed to update default rates:', error)
    }
  }

  /**
   * Clear rate cache
   */
  static clearCache(): void {
    this.rateCache.clear()
  }

  /**
   * Get supported currencies
   */
  static getSupportedCurrencies(): string[] {
    return ['KWD', 'USD', 'GBP']
  }

  /**
   * Validate currency code
   */
  static isValidCurrency(currency: string): boolean {
    return this.getSupportedCurrencies().includes(currency)
  }
}

export default CurrencyConverter
