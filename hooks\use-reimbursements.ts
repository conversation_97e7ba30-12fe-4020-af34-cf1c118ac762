"use client"

import { useState, useEffect } from 'react'
import { db, realtime } from '@/lib/supabase'
import { useBusiness } from '@/contexts/business-context'
import { Database } from '@/types/database'

type Reimbursement = Database['public']['Tables']['reimbursements']['Row']
type ReimbursementInsert = Database['public']['Tables']['reimbursements']['Insert']
type ReimbursementUpdate = Database['public']['Tables']['reimbursements']['Update']

interface ReimbursementWithPayment extends Reimbursement {
  payments?: {
    amount: number
    currency: string
    payment_date: string
    description: string | null
  } | null
}

export function useReimbursements() {
  const [reimbursements, setReimbursements] = useState<ReimbursementWithPayment[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const { currentBusiness } = useBusiness()

  useEffect(() => {
    if (currentBusiness) {
      loadReimbursements()
      
      // Set up real-time subscription
      const subscription = realtime.subscribeToReimbursements(
        currentBusiness.id,
        (payload) => {
          console.log('Real-time reimbursement update:', payload)
          
          switch (payload.eventType) {
            case 'INSERT':
              // Reload to get complete data with payment info
              loadReimbursements()
              break
            case 'UPDATE':
              setReimbursements(prev => 
                prev.map(reimbursement => 
                  reimbursement.id === payload.new.id ? { ...reimbursement, ...payload.new } : reimbursement
                )
              )
              break
            case 'DELETE':
              setReimbursements(prev => 
                prev.filter(reimbursement => reimbursement.id !== payload.old.id)
              )
              break
          }
        }
      )

      return () => {
        subscription.unsubscribe()
      }
    } else {
      setReimbursements([])
      setLoading(false)
    }
  }, [currentBusiness])

  const loadReimbursements = async () => {
    if (!currentBusiness) return

    try {
      setLoading(true)
      setError(null)
      
      const { data, error } = await db.reimbursements.getAll(currentBusiness.id)
      
      if (error) {
        setError(error.message)
        return
      }

      setReimbursements(data || [])
    } catch (err) {
      setError('Failed to load reimbursements')
      console.error('Error loading reimbursements:', err)
    } finally {
      setLoading(false)
    }
  }

  const createReimbursement = async (reimbursementData: Omit<ReimbursementInsert, 'business_id'>) => {
    if (!currentBusiness) throw new Error('No business selected')

    try {
      const { data, error } = await db.reimbursements.create({
        ...reimbursementData,
        business_id: currentBusiness.id,
      })

      if (error) throw error

      // Reload to get complete data with payment info
      await loadReimbursements()

      return data
    } catch (err) {
      console.error('Error creating reimbursement:', err)
      throw err
    }
  }

  const updateReimbursement = async (id: string, updates: ReimbursementUpdate) => {
    try {
      const { data, error } = await db.reimbursements.update(id, updates)

      if (error) throw error

      // Update local state
      setReimbursements(prev => 
        prev.map(reimbursement => 
          reimbursement.id === id ? { ...reimbursement, ...data } : reimbursement
        )
      )

      return data
    } catch (err) {
      console.error('Error updating reimbursement:', err)
      throw err
    }
  }

  const deleteReimbursement = async (id: string) => {
    try {
      const { error } = await db.reimbursements.delete(id)

      if (error) throw error

      setReimbursements(prev => prev.filter(reimbursement => reimbursement.id !== id))
    } catch (err) {
      console.error('Error deleting reimbursement:', err)
      throw err
    }
  }

  const getReimbursementsByStatus = (status: 'pending' | 'approved' | 'rejected' | 'paid') => {
    return reimbursements.filter(reimbursement => reimbursement.status === status)
  }

  const getTotalPendingAmount = () => {
    return reimbursements
      .filter(reimbursement => reimbursement.status === 'pending')
      .reduce((total, reimbursement) => total + reimbursement.amount, 0)
  }

  const getTotalApprovedAmount = () => {
    return reimbursements
      .filter(reimbursement => reimbursement.status === 'approved')
      .reduce((total, reimbursement) => total + reimbursement.amount, 0)
  }

  const getTotalPaidAmount = () => {
    return reimbursements
      .filter(reimbursement => reimbursement.status === 'paid')
      .reduce((total, reimbursement) => total + reimbursement.amount, 0)
  }

  const getReimbursementsByDateRange = (startDate: Date, endDate: Date) => {
    return reimbursements.filter(reimbursement => {
      const submittedDate = new Date(reimbursement.submitted_date)
      return submittedDate >= startDate && submittedDate <= endDate
    })
  }

  const getMonthlyReimbursementTrends = (months: number = 6) => {
    const trends = []
    const now = new Date()
    
    for (let i = months - 1; i >= 0; i--) {
      const monthDate = new Date(now.getFullYear(), now.getMonth() - i, 1)
      const startOfMonth = new Date(monthDate.getFullYear(), monthDate.getMonth(), 1)
      const endOfMonth = new Date(monthDate.getFullYear(), monthDate.getMonth() + 1, 0)
      
      const monthReimbursements = getReimbursementsByDateRange(startOfMonth, endOfMonth)
      
      const totalSubmitted = monthReimbursements.reduce((sum, reimbursement) => 
        sum + reimbursement.amount, 0
      )
      
      const totalPaid = monthReimbursements
        .filter(r => r.status === 'paid')
        .reduce((sum, reimbursement) => sum + reimbursement.amount, 0)
      
      trends.push({
        month: monthDate.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        totalSubmitted,
        totalPaid,
        count: monthReimbursements.length,
        date: monthDate,
      })
    }
    
    return trends
  }

  const refreshReimbursements = async () => {
    await loadReimbursements()
  }

  return {
    reimbursements,
    loading,
    error,
    createReimbursement,
    updateReimbursement,
    deleteReimbursement,
    getReimbursementsByStatus,
    getTotalPendingAmount,
    getTotalApprovedAmount,
    getTotalPaidAmount,
    getReimbursementsByDateRange,
    getMonthlyReimbursementTrends,
    refreshReimbursements,
  }
}
