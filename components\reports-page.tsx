"use client"

import { useState, useEffect } from "react"
import { Download, Calendar, TrendingUp, <PERSON><PERSON><PERSON>, <PERSON>Chart3, Loader2 } from "lucide-react"
import { format, startOfMonth, endOfMonth, subMonths, parseISO } from "date-fns"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DatePickerWithRange } from "@/components/ui/date-range-picker"
import type { DateRange } from "react-day-picker"
import { SharedLayout } from "@/components/shared-layout"
import { usePayments } from "@/hooks/use-payments"
import { useReimbursements } from "@/hooks/use-reimbursements"
import { useSubscriptions } from "@/hooks/use-subscriptions"
import { useBusiness } from "@/contexts/business-context"
import { CurrencyConverter } from "@/lib/currency"

export function ReportsPage() {
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: startOfMonth(new Date()),
    to: endOfMonth(new Date()),
  })
  const [reportType, setReportType] = useState("overview")

  // Use Supabase hooks
  const { currentBusiness } = useBusiness()
  const { payments, loading: paymentsLoading, error: paymentsError } = usePayments()
  const { reimbursements, loading: reimbursementsLoading, error: reimbursementsError } = useReimbursements()
  const { subscriptions, loading: subscriptionsLoading, error: subscriptionsError } = useSubscriptions()

  const loading = paymentsLoading || reimbursementsLoading || subscriptionsLoading
  const error = paymentsError || reimbursementsError || subscriptionsError

  if (!currentBusiness) {
    return (
      <SharedLayout activeUrl="/reports">
        <div className="flex flex-1 items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">No Business Selected</h2>
            <p className="text-muted-foreground">Please create or select a business to view reports.</p>
          </div>
        </div>
      </SharedLayout>
    )
  }

  const filteredPayments = payments.filter((payment) => {
    if (!dateRange?.from || !dateRange?.to) return true
    const paymentDate = new Date(payment.payment_date)
    return paymentDate >= dateRange.from && paymentDate <= dateRange.to
  })

  const filteredReimbursements = reimbursements.filter((reimb) => {
    if (!dateRange?.from || !dateRange?.to) return true
    const reimbDate = new Date(reimb.submitted_date)
    return reimbDate >= dateRange.from && reimbDate <= dateRange.to
  })

  // Calculate metrics
  const totalSpentKwd = filteredPayments.reduce((sum, p) => sum + (p.kwd_amount || p.amount), 0)
  const totalSpentUsd = filteredPayments
    .filter((p) => p.currency === "USD")
    .reduce((sum, p) => sum + p.amount, 0)
  const totalSpentGbp = filteredPayments
    .filter((p) => p.currency === "GBP")
    .reduce((sum, p) => sum + p.amount, 0)

  const totalReimbursedKwd = filteredReimbursements
    .filter((r) => r.status === "approved" || r.status === "paid")
    .reduce((sum, r) => sum + r.amount, 0)
  const pendingReimbursementsKwd = filteredReimbursements
    .filter((r) => r.status === "pending")
    .reduce((sum, r) => sum + r.amount, 0)

  // Category breakdown
  const categoryBreakdown = filteredPayments.reduce(
    (acc, payment) => {
      const subscription = subscriptions.find((s) => s.id === payment.subscription_id)
      const category = subscription?.category || "Other"
      acc[category] = (acc[category] || 0) + (payment.kwd_amount || payment.amount)
      return acc
    },
    {} as Record<string, number>,
  )

  // Monthly trends (last 6 months)
  const monthlyTrends = Array.from({ length: 6 }, (_, i) => {
    const month = subMonths(new Date(), i)
    const monthStart = startOfMonth(month)
    const monthEnd = endOfMonth(month)

    const monthPayments = payments.filter((p) => {
      const paymentDate = new Date(p.payment_date)
      return paymentDate >= monthStart && paymentDate <= monthEnd
    })

    return {
      month: format(month, "MMM yyyy"),
      totalKwd: monthPayments.reduce((sum, p) => sum + (p.kwd_amount || p.amount), 0),
      count: monthPayments.length,
    }
  }).reverse()

  const exportToCSV = () => {
    const csvData = [
      ["Date", "Subscription", "Original Amount", "Currency", "KWD Amount", "Exchange Rate", "Category", "Status"],
      ...filteredPayments.map((payment) => {
        const subscription = subscriptions.find((s) => s.id === payment.subscription_id)
        return [
          format(new Date(payment.payment_date), "yyyy-MM-dd"),
          subscription?.name || payment.description || "N/A",
          payment.amount.toFixed(2),
          payment.currency,
          (payment.kwd_amount || payment.amount).toFixed(2),
          (payment.exchange_rate || 1).toFixed(3),
          subscription?.category || "Other",
          payment.status,
        ]
      }),
    ]

    const csvContent = csvData.map((row) => row.join(",")).join("\n")
    const blob = new Blob([csvContent], { type: "text/csv" })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `subscription-report-${format(new Date(), "yyyy-MM-dd")}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  if (loading) {
    return (
      <SharedLayout activeUrl="/reports">
        <div className="flex flex-1 items-center justify-center">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Loading reports...</p>
          </div>
        </div>
      </SharedLayout>
    )
  }

  if (error) {
    return (
      <SharedLayout activeUrl="/reports">
        <div className="flex flex-1 items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2 text-destructive">Error Loading Reports</h2>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </div>
        </div>
      </SharedLayout>
    )
  }

  return (
    <SharedLayout activeUrl="/reports">
      <div className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Reports</h1>
            <p className="text-muted-foreground">Analyze your subscription spending and trends for {currentBusiness.name}</p>
          </div>

          <Button onClick={exportToCSV} disabled={filteredPayments.length === 0}>
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Report Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <label className="text-sm font-medium">Date Range</label>
                <DatePickerWithRange date={dateRange} setDate={setDateRange} />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Report Type</label>
                <Select value={reportType} onValueChange={setReportType}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="overview">Overview</SelectItem>
                    <SelectItem value="payments">Payments Analysis</SelectItem>
                    <SelectItem value="reimbursements">Reimbursements</SelectItem>
                    <SelectItem value="trends">Trends</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-end">
                <Button
                  variant="outline"
                  onClick={() => {
                    setDateRange({
                      from: startOfMonth(new Date()),
                      to: endOfMonth(new Date()),
                    })
                    setReportType("overview")
                  }}
                >
                  Reset Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Overview Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Spent (KWD)</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalSpentKwd.toFixed(2)} KWD</div>
              <div className="text-xs text-muted-foreground space-y-1 mt-2">
                <div>${totalSpentUsd.toFixed(2)} USD</div>
                <div>£{totalSpentGbp.toFixed(2)} GBP</div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Payments</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{filteredPayments.length}</div>
              <p className="text-xs text-muted-foreground">In selected period</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Reimbursed (KWD)</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{totalReimbursedKwd.toFixed(2)} KWD</div>
              <p className="text-xs text-muted-foreground">Approved reimbursements</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending (KWD)</CardTitle>
              <PieChart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{pendingReimbursementsKwd.toFixed(2)} KWD</div>
              <p className="text-xs text-muted-foreground">Pending reimbursements</p>
            </CardContent>
          </Card>
        </div>

        {/* Category Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Spending by Category</CardTitle>
            <CardDescription>Breakdown of expenses by subscription category</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(categoryBreakdown)
                .sort(([, a], [, b]) => b - a)
                .map(([category, amount]) => {
                  const percentage = (amount / totalSpentKwd) * 100
                  return (
                    <div key={category} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="font-medium">{category}</span>
                        <span>
                          {amount.toFixed(2)} KWD ({percentage.toFixed(1)}%)
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-blue-600 h-2 rounded-full" style={{ width: `${percentage}%` }} />
                      </div>
                    </div>
                  )
                })}
            </div>
          </CardContent>
        </Card>

        {/* Monthly Trends */}
        <Card>
          <CardHeader>
            <CardTitle>Monthly Spending Trends</CardTitle>
            <CardDescription>Last 6 months spending pattern</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {monthlyTrends.map((month, index) => {
                const maxAmount = Math.max(...monthlyTrends.map((m) => m.totalKwd))
                const percentage = maxAmount > 0 ? (month.totalKwd / maxAmount) * 100 : 0

                return (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="font-medium">{month.month}</span>
                      <span>
                        {month.totalKwd.toFixed(2)} KWD ({month.count} payments)
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className="bg-green-600 h-3 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Detailed Transactions */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Transactions</CardTitle>
            <CardDescription>Detailed view of payments in selected period</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredPayments.slice(0, 10).map((payment) => {
                const subscription = subscriptions.find((s) => s.name === payment.subscriptionName)
                return (
                  <div key={payment.id} className="flex justify-between items-center p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">{payment.subscriptionName}</div>
                      <div className="text-sm text-muted-foreground">
                        {format(parseISO(payment.paymentDate), "MMM dd, yyyy")} • {subscription?.category || "Other"}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold">{payment.kwdAmount.toFixed(2)} KWD</div>
                      <div className="text-sm text-muted-foreground">
                        {payment.originalAmount.toFixed(2)} {payment.originalCurrency}
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </SharedLayout>
  )
}
